import React from 'react';
import { Link } from 'react-router-dom';
import { 
  FiCamera, 
  FiMail, 
  FiPhone, 
  FiMapPin,
  FiFacebook,
  FiTwitter,
  FiInstagram,
  FiLinkedin
} from 'react-icons/fi';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    product: [
      { name: '<PERSON><PERSON><PERSON>', href: '/dashboard' },
      { name: 'Rekomendasi Produk', href: '/dashboard' },
      { name: 'Riwayat Analisis', href: '/history' },
      { name: 'Artikel Skincare', href: '/articles' },
    ],
    company: [
      { name: '<PERSON><PERSON><PERSON>', href: '/about' },
      { name: '<PERSON>', href: '/about#team' },
      { name: '<PERSON><PERSON><PERSON>', href: '/careers' },
      { name: 'Blog', href: '/articles' },
    ],
    support: [
      { name: '<PERSON><PERSON><PERSON>', href: '/help' },
      { name: '<PERSON><PERSON>', href: '/faq' },
      { name: '<PERSON><PERSON>', href: '/contact' },
      { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/privacy' },
    ],
    legal: [
      { name: 'Syarat & Ketentuan', href: '/terms' },
      { name: 'Kebijakan Privasi', href: '/privacy' },
      { name: 'Kebijakan Cookie', href: '/cookies' },
      { name: 'Disclaimer', href: '/disclaimer' },
    ]
  };

  const socialLinks = [
    { name: 'Facebook', icon: FiFacebook, href: '#' },
    { name: 'Twitter', icon: FiTwitter, href: '#' },
    { name: 'Instagram', icon: FiInstagram, href: '#' },
    { name: 'LinkedIn', icon: FiLinkedin, href: '#' },
  ];

  return (
    <footer className="bg-neutral-900 text-neutral-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 gradient-bg rounded-lg flex items-center justify-center">
                <FiCamera className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-white">SkinCare AI</span>
            </div>
            <p className="text-neutral-400 mb-6 max-w-md">
              Platform analisis kulit berbasis AI yang memberikan rekomendasi produk skincare 
              yang tepat untuk kebutuhan kulit Anda.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <FiMail className="w-4 h-4 text-primary-400" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <FiPhone className="w-4 h-4 text-primary-400" />
                <span className="text-sm">+62 21 1234 5678</span>
              </div>
              <div className="flex items-center space-x-3">
                <FiMapPin className="w-4 h-4 text-primary-400" />
                <span className="text-sm">Jakarta, Indonesia</span>
              </div>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Produk</h3>
            <ul className="space-y-3">
              {footerLinks.product.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-sm hover:text-primary-400 transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Perusahaan</h3>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-sm hover:text-primary-400 transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Dukungan</h3>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-sm hover:text-primary-400 transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="py-6 border-t border-neutral-800">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <div className="text-sm text-neutral-400">
              © {currentYear} SkinCare AI. All rights reserved.
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  className="p-2 rounded-lg bg-neutral-800 hover:bg-neutral-700 transition-colors duration-200"
                  aria-label={social.name}
                >
                  <social.icon className="w-4 h-4" />
                </a>
              ))}
            </div>

            {/* Legal Links */}
            <div className="flex items-center space-x-6">
              {footerLinks.legal.slice(0, 2).map((link) => (
                <Link
                  key={link.name}
                  to={link.href}
                  className="text-sm text-neutral-400 hover:text-primary-400 transition-colors duration-200"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
