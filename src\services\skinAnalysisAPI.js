import api, { API_ENDPOINTS } from './api';

// Skin Analysis API functions
export const skinAnalysisAPI = {
  // Analyze skin from uploaded image
  analyzeSkin: async (imageFile, additionalData = {}) => {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);
      
      // Add additional data if provided
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });

      const response = await api.post(API_ENDPOINTS.SKIN_ANALYSIS.ANALYZE, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 60000, // 60 seconds for AI processing
      });

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to analyze skin');
    }
  },

  // Get analysis history
  getAnalysisHistory: async (page = 1, limit = 10) => {
    try {
      const response = await api.get(API_ENDPOINTS.SKIN_ANALYSIS.HISTORY, {
        params: { page, limit }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch analysis history');
    }
  },

  // Get specific analysis result
  getAnalysisResult: async (analysisId) => {
    try {
      const response = await api.get(`${API_ENDPOINTS.SKIN_ANALYSIS.RESULT}/${analysisId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch analysis result');
    }
  },

  // Delete analysis record
  deleteAnalysis: async (analysisId) => {
    try {
      const response = await api.delete(`${API_ENDPOINTS.SKIN_ANALYSIS.RESULT}/${analysisId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete analysis');
    }
  },

  // Get skin analysis statistics
  getAnalysisStats: async () => {
    try {
      const response = await api.get(`${API_ENDPOINTS.SKIN_ANALYSIS.HISTORY}/stats`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch analysis statistics');
    }
  }
};

export default skinAnalysisAPI;
