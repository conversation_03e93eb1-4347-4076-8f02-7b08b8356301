import React, { useState, useRef, useCallback } from 'react';
import { 
  FiCamera, 
  FiRotateCcw, 
  FiX, 
  FiCheck,
  FiAlertCircle,
  FiLoader
} from 'react-icons/fi';
import { skinAnalysisAPI } from '../../services/skinAnalysisAPI';

const CameraCapture = ({ onAnalysisComplete, onCancel }) => {
  const [stream, setStream] = useState(null);
  const [capturedImage, setCapturedImage] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState('');
  const [cameraStarted, setCameraStarted] = useState(false);
  
  const videoRef = useRef(null);
  const canvasRef = useRef(null);

  const startCamera = useCallback(async () => {
    try {
      setError('');
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        }
      });
      
      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
      setCameraStarted(true);
    } catch (err) {
      setError('Tidak dapat mengakses kamera. Pastikan Anda memberikan izin kamera.');
      console.error('Camera access error:', err);
    }
  }, []);

  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setCameraStarted(false);
  }, [stream]);

  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw the video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert to blob
    canvas.toBlob((blob) => {
      setCapturedImage(blob);
      stopCamera();
    }, 'image/jpeg', 0.8);
  }, [stopCamera]);

  const retakePhoto = useCallback(() => {
    setCapturedImage(null);
    setError('');
    startCamera();
  }, [startCamera]);

  const analyzeImage = async () => {
    if (!capturedImage) return;

    try {
      setIsAnalyzing(true);
      setError('');

      const result = await skinAnalysisAPI.analyzeSkin(capturedImage);
      onAnalysisComplete(result);
    } catch (err) {
      setError(err.message || 'Gagal menganalisis gambar. Silakan coba lagi.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleCancel = () => {
    stopCamera();
    onCancel();
  };

  React.useEffect(() => {
    return () => {
      stopCamera();
    };
  }, [stopCamera]);

  return (
    <div className="card max-w-2xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-neutral-900">Analisis Kulit</h2>
        <button
          onClick={handleCancel}
          className="p-2 hover:bg-neutral-100 rounded-lg transition-colors duration-200"
        >
          <FiX className="w-5 h-5 text-neutral-600" />
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-3">
          <FiAlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
          <p className="text-red-800">{error}</p>
        </div>
      )}

      <div className="space-y-6">
        {/* Camera/Image Display */}
        <div className="relative bg-neutral-900 rounded-lg overflow-hidden aspect-video">
          {capturedImage ? (
            <img
              src={URL.createObjectURL(capturedImage)}
              alt="Captured"
              className="w-full h-full object-cover"
            />
          ) : cameraStarted ? (
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center text-white">
                <FiCamera className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg mb-4">Siap untuk mengambil foto?</p>
                <button
                  onClick={startCamera}
                  className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
                >
                  Mulai Kamera
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-900 mb-2">Tips untuk foto terbaik:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Pastikan wajah Anda terlihat jelas dan tidak tertutup</li>
            <li>• Gunakan pencahayaan yang cukup dan merata</li>
            <li>• Posisikan wajah di tengah frame</li>
            <li>• Hindari bayangan yang berlebihan</li>
            <li>• Lepaskan kacamata atau aksesoris yang menutupi wajah</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          {capturedImage ? (
            <>
              <button
                onClick={retakePhoto}
                className="btn-outline flex items-center justify-center space-x-2"
                disabled={isAnalyzing}
              >
                <FiRotateCcw className="w-5 h-5" />
                <span>Ambil Ulang</span>
              </button>
              <button
                onClick={analyzeImage}
                disabled={isAnalyzing}
                className="btn-primary flex items-center justify-center space-x-2 flex-1"
              >
                {isAnalyzing ? (
                  <>
                    <FiLoader className="w-5 h-5 animate-spin" />
                    <span>Menganalisis...</span>
                  </>
                ) : (
                  <>
                    <FiCheck className="w-5 h-5" />
                    <span>Analisis Foto</span>
                  </>
                )}
              </button>
            </>
          ) : cameraStarted ? (
            <button
              onClick={capturePhoto}
              className="btn-primary flex items-center justify-center space-x-2 w-full"
            >
              <FiCamera className="w-5 h-5" />
              <span>Ambil Foto</span>
            </button>
          ) : null}
        </div>

        {isAnalyzing && (
          <div className="text-center py-8">
            <div className="animate-pulse">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FiLoader className="w-8 h-8 text-primary-600 animate-spin" />
              </div>
              <p className="text-neutral-600 mb-2">Menganalisis kondisi kulit Anda...</p>
              <p className="text-sm text-neutral-500">Proses ini membutuhkan beberapa detik</p>
            </div>
          </div>
        )}
      </div>

      {/* Hidden canvas for image capture */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
};

export default CameraCapture;
