import React, { useState } from 'react';
import { 
  FiCamera, 
  FiUpload, 
  FiInfo,
  FiCheckCircle,
  FiAlertCircle
} from 'react-icons/fi';
import CameraCapture from '../components/SkinAnalysis/CameraCapture';
import AnalysisResult from '../components/SkinAnalysis/AnalysisResult';

const Analysis = () => {
  const [currentStep, setCurrentStep] = useState('intro'); // intro, capture, result
  const [analysisResult, setAnalysisResult] = useState(null);

  const handleAnalysisComplete = (result) => {
    setAnalysisResult(result);
    setCurrentStep('result');
  };

  const handleNewAnalysis = () => {
    setAnalysisResult(null);
    setCurrentStep('intro');
  };

  const startAnalysis = () => {
    setCurrentStep('capture');
  };

  const steps = [
    {
      number: 1,
      title: 'Persiapan',
      description: 'Pastikan pencahayaan baik dan wajah terlihat jelas',
      active: currentStep === 'intro'
    },
    {
      number: 2,
      title: 'Ambil Foto',
      description: 'Gunakan kamera untuk mengambil foto wajah Anda',
      active: currentStep === 'capture'
    },
    {
      number: 3,
      title: 'Hasil Analisis',
      description: 'Lihat hasil analisis dan rekomendasi produk',
      active: currentStep === 'result'
    }
  ];

  const tips = [
    {
      icon: FiCheckCircle,
      text: 'Gunakan pencahayaan alami atau lampu yang terang',
      type: 'success'
    },
    {
      icon: FiCheckCircle,
      text: 'Posisikan wajah di tengah frame kamera',
      type: 'success'
    },
    {
      icon: FiCheckCircle,
      text: 'Pastikan wajah tidak tertutup rambut atau aksesoris',
      type: 'success'
    },
    {
      icon: FiAlertCircle,
      text: 'Hindari menggunakan makeup tebal saat analisis',
      type: 'warning'
    },
    {
      icon: FiAlertCircle,
      text: 'Jangan menggunakan filter atau edit foto',
      type: 'warning'
    }
  ];

  if (currentStep === 'capture') {
    return (
      <div className="min-h-screen bg-neutral-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <CameraCapture 
            onAnalysisComplete={handleAnalysisComplete}
            onCancel={() => setCurrentStep('intro')}
          />
        </div>
      </div>
    );
  }

  if (currentStep === 'result') {
    return (
      <div className="min-h-screen bg-neutral-50 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <AnalysisResult 
            analysis={analysisResult}
            onNewAnalysis={handleNewAnalysis}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-neutral-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-neutral-900 mb-4">
            Analisis Kulit AI
          </h1>
          <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
            Dapatkan analisis mendalam tentang kondisi kulit Anda dan rekomendasi produk yang tepat
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-12">
          <div className="flex items-center justify-center space-x-8">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${
                    step.active 
                      ? 'bg-primary-600 text-white' 
                      : 'bg-neutral-200 text-neutral-600'
                  }`}>
                    {step.number}
                  </div>
                  <div className="mt-2 text-center">
                    <div className={`font-medium ${
                      step.active ? 'text-primary-600' : 'text-neutral-600'
                    }`}>
                      {step.title}
                    </div>
                    <div className="text-sm text-neutral-500 max-w-24">
                      {step.description}
                    </div>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className="w-16 h-0.5 bg-neutral-200 mx-4 mt-6"></div>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <div className="card text-center">
              <div className="py-12">
                <div className="w-32 h-32 gradient-bg rounded-full flex items-center justify-center mx-auto mb-8">
                  <FiCamera className="w-16 h-16 text-white" />
                </div>
                
                <h2 className="text-3xl font-bold text-neutral-900 mb-4">
                  Mulai Analisis Kulit Anda
                </h2>
                
                <p className="text-lg text-neutral-600 mb-8 max-w-lg mx-auto">
                  Teknologi AI kami akan menganalisis kondisi kulit Anda dalam hitungan detik 
                  dan memberikan rekomendasi produk yang personal
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                  <button
                    onClick={startAnalysis}
                    className="btn-primary text-lg px-8 py-3 flex items-center justify-center space-x-3"
                  >
                    <FiCamera className="w-6 h-6" />
                    <span>Mulai Analisis</span>
                  </button>
                  
                  <button className="btn-outline text-lg px-8 py-3 flex items-center justify-center space-x-3">
                    <FiUpload className="w-6 h-6" />
                    <span>Upload Foto</span>
                  </button>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <div className="flex items-center justify-center space-x-2 mb-4">
                    <FiInfo className="w-5 h-5 text-blue-600" />
                    <h3 className="font-semibold text-blue-900">Informasi Penting</h3>
                  </div>
                  <p className="text-blue-800 text-sm">
                    Analisis ini menggunakan teknologi AI untuk memberikan estimasi kondisi kulit. 
                    Hasil tidak menggantikan konsultasi dengan dermatologis profesional.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Tips Card */}
            <div className="card">
              <h3 className="text-xl font-bold text-neutral-900 mb-6">
                Tips untuk Hasil Terbaik
              </h3>
              
              <div className="space-y-4">
                {tips.map((tip, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <tip.icon className={`w-5 h-5 mt-0.5 flex-shrink-0 ${
                      tip.type === 'success' ? 'text-green-600' : 'text-yellow-600'
                    }`} />
                    <p className={`text-sm ${
                      tip.type === 'success' ? 'text-green-800' : 'text-yellow-800'
                    }`}>
                      {tip.text}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Features Card */}
            <div className="card">
              <h3 className="text-xl font-bold text-neutral-900 mb-6">
                Apa yang Akan Anda Dapatkan
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                  <div>
                    <div className="font-medium text-neutral-900">Analisis Mendalam</div>
                    <div className="text-sm text-neutral-600">
                      Kondisi kulit, tipe kulit, dan area yang perlu perhatian
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                  <div>
                    <div className="font-medium text-neutral-900">Skor Kesehatan</div>
                    <div className="text-sm text-neutral-600">
                      Rating numerik dari kondisi kulit Anda saat ini
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                  <div>
                    <div className="font-medium text-neutral-900">Rekomendasi Produk</div>
                    <div className="text-sm text-neutral-600">
                      Produk skincare yang sesuai dengan kebutuhan kulit Anda
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                  <div>
                    <div className="font-medium text-neutral-900">Tips Perawatan</div>
                    <div className="text-sm text-neutral-600">
                      Saran perawatan harian untuk memperbaiki kondisi kulit
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Stats Card */}
            <div className="card">
              <h3 className="text-xl font-bold text-neutral-900 mb-6">
                Statistik Platform
              </h3>
              
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600">50K+</div>
                  <div className="text-sm text-neutral-600">Analisis Selesai</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600">95%</div>
                  <div className="text-sm text-neutral-600">Tingkat Akurasi</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600">4.8/5</div>
                  <div className="text-sm text-neutral-600">Rating Pengguna</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analysis;
