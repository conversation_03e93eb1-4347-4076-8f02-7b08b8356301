# Firebase Configuration
REACT_APP_FIREBASE_API_KEY=your_firebase_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
REACT_APP_FIREBASE_PROJECT_ID=your_firebase_project_id
REACT_APP_FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
REACT_APP_FIREBASE_APP_ID=your_firebase_app_id
REACT_APP_FIREBASE_MEASUREMENT_ID=your_firebase_measurement_id

# Backend API Configuration
REACT_APP_API_BASE_URL=http://localhost:8000/api
REACT_APP_AI_MODEL_ENDPOINT=http://localhost:8000/api/analyze
REACT_APP_PRODUCTS_ENDPOINT=http://localhost:8000/api/products
REACT_APP_ARTICLES_ENDPOINT=http://localhost:8000/api/articles

# Environment
REACT_APP_ENVIRONMENT=development
