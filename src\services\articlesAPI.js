import api, { API_ENDPOINTS } from './api';

// Articles API functions
export const articlesAPI = {
  // Get all articles with pagination and filters
  getArticles: async (params = {}) => {
    try {
      const {
        page = 1,
        limit = 10,
        category = '',
        search = '',
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = params;

      const response = await api.get(API_ENDPOINTS.ARTICLES.LIST, {
        params: {
          page,
          limit,
          category,
          search,
          sortBy,
          sortOrder
        }
      });

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch articles');
    }
  },

  // Get article by ID
  getArticleById: async (articleId) => {
    try {
      const response = await api.get(`${API_ENDPOINTS.ARTICLES.DETAILS}/${articleId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch article');
    }
  },

  // Get featured articles
  getFeaturedArticles: async (limit = 5) => {
    try {
      const response = await api.get(`${API_ENDPOINTS.ARTICLES.LIST}/featured`, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch featured articles');
    }
  },

  // Get articles by category
  getArticlesByCategory: async (category, params = {}) => {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = params;

      const response = await api.get(`${API_ENDPOINTS.ARTICLES.LIST}/category/${category}`, {
        params: {
          page,
          limit,
          sortBy,
          sortOrder
        }
      });

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch articles by category');
    }
  },

  // Get article categories
  getCategories: async () => {
    try {
      const response = await api.get(API_ENDPOINTS.ARTICLES.CATEGORIES);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch categories');
    }
  },

  // Search articles
  searchArticles: async (query, params = {}) => {
    try {
      const {
        page = 1,
        limit = 10,
        category = '',
        sortBy = 'relevance',
        sortOrder = 'desc'
      } = params;

      const response = await api.get(`${API_ENDPOINTS.ARTICLES.LIST}/search`, {
        params: {
          q: query,
          page,
          limit,
          category,
          sortBy,
          sortOrder
        }
      });

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to search articles');
    }
  },

  // Get related articles
  getRelatedArticles: async (articleId, limit = 4) => {
    try {
      const response = await api.get(`${API_ENDPOINTS.ARTICLES.DETAILS}/${articleId}/related`, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch related articles');
    }
  }
};

export default articlesAPI;
