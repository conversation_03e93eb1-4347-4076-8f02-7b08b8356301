import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 30000, // 30 seconds timeout for AI processing
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    
    // Log error for debugging
    console.error('API Error:', error.response?.data || error.message);
    
    return Promise.reject(error);
  }
);

// API endpoints configuration
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
  },
  
  // Skin Analysis
  SKIN_ANALYSIS: {
    ANALYZE: '/analyze/skin',
    HISTORY: '/analyze/history',
    RESULT: '/analyze/result',
  },
  
  // Products
  PRODUCTS: {
    RECOMMENDATIONS: '/products/recommendations',
    SEARCH: '/products/search',
    DETAILS: '/products',
  },
  
  // Articles
  ARTICLES: {
    LIST: '/articles',
    DETAILS: '/articles',
    CATEGORIES: '/articles/categories',
  },
  
  // User
  USER: {
    PROFILE: '/user/profile',
    HISTORY: '/user/history',
    PREFERENCES: '/user/preferences',
  }
};

export default api;
