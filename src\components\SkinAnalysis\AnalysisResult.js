import React, { useState, useEffect } from 'react';
import { 
  FiStar, 
  FiTrendingUp, 
  FiTrendingDown,
  FiInfo,
  FiShoppingBag,
  FiRefreshCw,
  FiDownload,
  FiShare2
} from 'react-icons/fi';
import ProductRecommendation from './ProductRecommendation';

const AnalysisResult = ({ analysis, onNewAnalysis }) => {
  const [showRecommendations, setShowRecommendations] = useState(false);

  // Mock data structure - replace with actual API response structure
  const mockAnalysis = {
    id: analysis?.id || 'mock-id',
    score: analysis?.score || 7.5,
    skinType: analysis?.skinType || 'Kombinasi',
    concerns: analysis?.concerns || ['Jerawat', 'Pori-pori besar', 'Kulit kusam'],
    strengths: analysis?.strengths || ['Hidrasi baik', 'Elastisitas normal'],
    recommendations: analysis?.recommendations || [],
    image: analysis?.image || null,
    createdAt: analysis?.createdAt || new Date().toISOString(),
    details: analysis?.details || {
      hydration: 75,
      oiliness: 60,
      sensitivity: 30,
      acne: 40,
      wrinkles: 20,
      pigmentation: 35
    }
  };

  const getScoreColor = (score) => {
    if (score >= 8) return 'text-green-600';
    if (score >= 6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score) => {
    if (score >= 8) return 'Sangat Baik';
    if (score >= 6) return 'Baik';
    if (score >= 4) return 'Cukup';
    return 'Perlu Perhatian';
  };

  const getProgressColor = (value) => {
    if (value >= 70) return 'bg-green-500';
    if (value >= 40) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const skinMetrics = [
    { label: 'Hidrasi', value: mockAnalysis.details.hydration, unit: '%' },
    { label: 'Minyak', value: mockAnalysis.details.oiliness, unit: '%' },
    { label: 'Sensitivitas', value: mockAnalysis.details.sensitivity, unit: '%' },
    { label: 'Jerawat', value: mockAnalysis.details.acne, unit: '%' },
    { label: 'Kerutan', value: mockAnalysis.details.wrinkles, unit: '%' },
    { label: 'Pigmentasi', value: mockAnalysis.details.pigmentation, unit: '%' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="card">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-neutral-900 mb-2">Hasil Analisis Kulit</h2>
            <p className="text-neutral-600">
              Analisis dilakukan pada {new Date(mockAnalysis.createdAt).toLocaleDateString('id-ID', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
          </div>
          <div className="flex items-center space-x-3 mt-4 md:mt-0">
            <button
              onClick={onNewAnalysis}
              className="btn-outline flex items-center space-x-2"
            >
              <FiRefreshCw className="w-4 h-4" />
              <span>Analisis Baru</span>
            </button>
            <button className="p-2 hover:bg-neutral-100 rounded-lg transition-colors duration-200">
              <FiDownload className="w-5 h-5 text-neutral-600" />
            </button>
            <button className="p-2 hover:bg-neutral-100 rounded-lg transition-colors duration-200">
              <FiShare2 className="w-5 h-5 text-neutral-600" />
            </button>
          </div>
        </div>

        {/* Overall Score */}
        <div className="text-center py-8 border-b border-neutral-200">
          <div className={`text-6xl font-bold mb-2 ${getScoreColor(mockAnalysis.score)}`}>
            {mockAnalysis.score}
          </div>
          <div className="text-xl font-semibold text-neutral-900 mb-2">
            {getScoreLabel(mockAnalysis.score)}
          </div>
          <div className="text-neutral-600">
            Skor Kesehatan Kulit Anda
          </div>
        </div>

        {/* Skin Type */}
        <div className="pt-6">
          <div className="flex items-center justify-center space-x-2">
            <FiInfo className="w-5 h-5 text-primary-600" />
            <span className="text-lg font-semibold text-neutral-900">
              Tipe Kulit: {mockAnalysis.skinType}
            </span>
          </div>
        </div>
      </div>

      {/* Detailed Metrics */}
      <div className="card">
        <h3 className="text-xl font-bold text-neutral-900 mb-6">Analisis Detail</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {skinMetrics.map((metric, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-neutral-700">{metric.label}</span>
                <span className="text-sm font-bold text-neutral-900">
                  {metric.value}{metric.unit}
                </span>
              </div>
              <div className="w-full bg-neutral-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-500 ${getProgressColor(metric.value)}`}
                  style={{ width: `${metric.value}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Concerns and Strengths */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Concerns */}
        <div className="card">
          <div className="flex items-center space-x-2 mb-4">
            <FiTrendingDown className="w-5 h-5 text-red-600" />
            <h3 className="text-lg font-bold text-neutral-900">Area yang Perlu Perhatian</h3>
          </div>
          <div className="space-y-3">
            {mockAnalysis.concerns.map((concern, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 bg-red-50 rounded-lg">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-red-800 font-medium">{concern}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Strengths */}
        <div className="card">
          <div className="flex items-center space-x-2 mb-4">
            <FiTrendingUp className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-bold text-neutral-900">Kelebihan Kulit Anda</h3>
          </div>
          <div className="space-y-3">
            {mockAnalysis.strengths.map((strength, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-green-800 font-medium">{strength}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recommendations CTA */}
      <div className="card text-center">
        <div className="py-8">
          <FiShoppingBag className="w-16 h-16 text-primary-600 mx-auto mb-4" />
          <h3 className="text-2xl font-bold text-neutral-900 mb-4">
            Rekomendasi Produk Skincare
          </h3>
          <p className="text-neutral-600 mb-6 max-w-md mx-auto">
            Berdasarkan analisis kulit Anda, kami telah menyiapkan rekomendasi produk 
            yang sesuai dengan kebutuhan kulit Anda
          </p>
          <button
            onClick={() => setShowRecommendations(true)}
            className="btn-primary flex items-center space-x-2 mx-auto"
          >
            <FiStar className="w-5 h-5" />
            <span>Lihat Rekomendasi</span>
          </button>
        </div>
      </div>

      {/* Product Recommendations Modal/Component */}
      {showRecommendations && (
        <ProductRecommendation
          analysisId={mockAnalysis.id}
          skinType={mockAnalysis.skinType}
          concerns={mockAnalysis.concerns}
          onClose={() => setShowRecommendations(false)}
        />
      )}
    </div>
  );
};

export default AnalysisResult;
