import React from 'react';
import { Link } from 'react-router-dom';
import { 
  FiCamera, 
  FiStar, 
  FiShield, 
  FiZap,
  FiUsers,
  FiTrendingUp,
  FiCheckCircle,
  FiArrowRight
} from 'react-icons/fi';

const LandingPage = () => {
  const features = [
    {
      icon: FiCamera,
      title: 'Analisis AI Canggih',
      description: 'Teknologi CNN untuk menganalisis kondisi kulit dengan akurasi tinggi'
    },
    {
      icon: FiStar,
      title: 'Rekomendasi Personal',
      description: 'Dapatkan rekomendasi produk skincare yang sesuai dengan kebutuhan kulit Anda'
    },
    {
      icon: FiShield,
      title: 'Data Aman',
      description: 'Privasi dan keamanan data Anda adalah prioritas utama kami'
    },
    {
      icon: FiZap,
      title: 'Hasil Instan',
      description: '<PERSON><PERSON><PERSON> cepat dengan hasil yang dapat Anda lihat dalam hitungan detik'
    }
  ];

  const stats = [
    { number: '10K+', label: 'Pengguna Aktif' },
    { number: '50K+', label: '<PERSON><PERSON><PERSON>' },
    { number: '95%', label: 'Tingkat Akurasi' },
    { number: '4.8', label: 'Rating Pengguna' }
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'Beauty Enthusiast',
      content: 'SkinCare AI membantu saya menemukan produk yang tepat untuk kulit sensitif saya. Hasilnya luar biasa!',
      rating: 5
    },
    {
      name: 'Maya Putri',
      role: 'Working Professional',
      content: 'Aplikasi yang sangat membantu untuk busy professional seperti saya. Rekomendasi produknya sangat akurat.',
      rating: 5
    },
    {
      name: 'Lisa Chen',
      role: 'Skincare Blogger',
      content: 'Teknologi AI-nya impressive! Saya merekomendasikan ini untuk semua yang ingin skincare routine yang tepat.',
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="gradient-bg text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Analisis Kulit dengan
              <span className="block text-yellow-300">Teknologi AI</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-primary-100 max-w-3xl mx-auto">
              Dapatkan rekomendasi produk skincare yang tepat berdasarkan analisis AI 
              dari foto wajah Anda
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/register"
                className="bg-white text-primary-600 hover:bg-primary-50 font-semibold py-3 px-8 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <span>Mulai Analisis</span>
                <FiArrowRight className="w-5 h-5" />
              </Link>
              <Link
                to="/about"
                className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-semibold py-3 px-8 rounded-lg transition-all duration-200"
              >
                Pelajari Lebih Lanjut
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Mengapa Memilih SkinCare AI?
            </h2>
            <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
              Platform terdepan untuk analisis kulit dan rekomendasi produk skincare
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 gradient-bg rounded-full flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-neutral-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-neutral-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2">
                  {stat.number}
                </div>
                <div className="text-neutral-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Cara Kerja
            </h2>
            <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
              Tiga langkah mudah untuk mendapatkan rekomendasi skincare yang tepat
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-primary-600">1</span>
              </div>
              <h3 className="text-xl font-semibold text-neutral-900 mb-4">
                Ambil Foto
              </h3>
              <p className="text-neutral-600">
                Ambil foto wajah Anda dengan pencahayaan yang baik menggunakan kamera
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-primary-600">2</span>
              </div>
              <h3 className="text-xl font-semibold text-neutral-900 mb-4">
                Analisis AI
              </h3>
              <p className="text-neutral-600">
                AI kami akan menganalisis kondisi kulit Anda menggunakan teknologi CNN
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-primary-600">3</span>
              </div>
              <h3 className="text-xl font-semibold text-neutral-900 mb-4">
                Dapatkan Rekomendasi
              </h3>
              <p className="text-neutral-600">
                Terima rekomendasi produk skincare yang sesuai dengan kebutuhan kulit Anda
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Apa Kata Pengguna Kami
            </h2>
            <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
              Ribuan pengguna telah merasakan manfaat SkinCare AI
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="card">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <FiStar key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-neutral-600 mb-6">
                  "{testimonial.content}"
                </p>
                <div>
                  <div className="font-semibold text-neutral-900">
                    {testimonial.name}
                  </div>
                  <div className="text-sm text-neutral-500">
                    {testimonial.role}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 gradient-bg text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Siap Untuk Memulai Perjalanan Skincare Anda?
          </h2>
          <p className="text-xl mb-8 text-primary-100">
            Bergabunglah dengan ribuan pengguna yang telah menemukan produk skincare yang tepat
          </p>
          <Link
            to="/register"
            className="bg-white text-primary-600 hover:bg-primary-50 font-semibold py-3 px-8 rounded-lg transition-colors duration-200 inline-flex items-center space-x-2"
          >
            <span>Mulai Sekarang</span>
            <FiArrowRight className="w-5 h-5" />
          </Link>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;
