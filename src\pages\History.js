import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>lock, 
  <PERSON>Eye, 
  FiTrash2, 
  FiDownload,
  FiFilter,
  FiSearch,
  FiCalendar
} from 'react-icons/fi';
import { skinAnalysisAPI } from '../services/skinAnalysisAPI';

const History = () => {
  const [analyses, setAnalyses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('date');
  const [filterBy, setFilterBy] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Mock data - replace with actual API call
  const mockAnalyses = [
    {
      id: 1,
      date: '2024-01-15',
      score: 8.5,
      skinType: 'Kombinasi',
      concerns: ['Jerawat', 'Pori-pori besar'],
      image: '/api/placeholder/100/100'
    },
    {
      id: 2,
      date: '2024-01-10',
      score: 7.2,
      skinType: 'Berminyak',
      concerns: ['Kulit kusam', 'Komedo'],
      image: '/api/placeholder/100/100'
    },
    {
      id: 3,
      date: '2024-01-05',
      score: 6.8,
      skinType: 'Kering',
      concerns: ['Kulit kering', 'Garis halus'],
      image: '/api/placeholder/100/100'
    },
    {
      id: 4,
      date: '2024-01-01',
      score: 9.1,
      skinType: 'Normal',
      concerns: ['Pencegahan aging'],
      image: '/api/placeholder/100/100'
    }
  ];

  useEffect(() => {
    loadAnalysisHistory();
  }, [currentPage, sortBy, filterBy]);

  const loadAnalysisHistory = async () => {
    try {
      setLoading(true);
      setError('');
      
      // In real implementation:
      // const response = await skinAnalysisAPI.getAnalysisHistory(currentPage, 10);
      // setAnalyses(response.data);
      // setTotalPages(response.totalPages);
      
      // For now, use mock data
      setTimeout(() => {
        setAnalyses(mockAnalyses);
        setTotalPages(1);
        setLoading(false);
      }, 500);
      
    } catch (err) {
      setError(err.message || 'Gagal memuat riwayat analisis');
      setLoading(false);
    }
  };

  const handleDelete = async (analysisId) => {
    if (!window.confirm('Apakah Anda yakin ingin menghapus analisis ini?')) {
      return;
    }

    try {
      await skinAnalysisAPI.deleteAnalysis(analysisId);
      setAnalyses(analyses.filter(analysis => analysis.id !== analysisId));
    } catch (err) {
      alert('Gagal menghapus analisis: ' + err.message);
    }
  };

  const getScoreColor = (score) => {
    if (score >= 8) return 'text-green-600 bg-green-100';
    if (score >= 6) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getScoreLabel = (score) => {
    if (score >= 8) return 'Sangat Baik';
    if (score >= 6) return 'Baik';
    if (score >= 4) return 'Cukup';
    return 'Perlu Perhatian';
  };

  const filteredAnalyses = analyses.filter(analysis => {
    const matchesSearch = analysis.skinType.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         analysis.concerns.some(concern => 
                           concern.toLowerCase().includes(searchTerm.toLowerCase())
                         );
    
    if (filterBy === 'all') return matchesSearch;
    if (filterBy === 'good') return matchesSearch && analysis.score >= 7;
    if (filterBy === 'needs-attention') return matchesSearch && analysis.score < 7;
    
    return matchesSearch;
  });

  const sortedAnalyses = [...filteredAnalyses].sort((a, b) => {
    switch (sortBy) {
      case 'date':
        return new Date(b.date) - new Date(a.date);
      case 'score':
        return b.score - a.score;
      case 'skinType':
        return a.skinType.localeCompare(b.skinType);
      default:
        return 0;
    }
  });

  return (
    <div className="min-h-screen bg-neutral-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">Riwayat Analisis</h1>
          <p className="text-neutral-600">
            Lihat dan kelola semua analisis kulit yang pernah Anda lakukan
          </p>
        </div>

        {/* Filters and Search */}
        <div className="card mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Cari berdasarkan tipe kulit atau masalah..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <FiFilter className="w-4 h-4 text-neutral-600" />
                <select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value)}
                  className="input-field w-auto"
                >
                  <option value="all">Semua</option>
                  <option value="good">Kondisi Baik</option>
                  <option value="needs-attention">Perlu Perhatian</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <FiCalendar className="w-4 h-4 text-neutral-600" />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="input-field w-auto"
                >
                  <option value="date">Tanggal Terbaru</option>
                  <option value="score">Skor Tertinggi</option>
                  <option value="skinType">Tipe Kulit</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Analysis List */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-neutral-600">Memuat riwayat analisis...</p>
          </div>
        ) : error ? (
          <div className="card text-center py-12">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={loadAnalysisHistory}
              className="btn-primary"
            >
              Coba Lagi
            </button>
          </div>
        ) : sortedAnalyses.length === 0 ? (
          <div className="card text-center py-12">
            <FiClock className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-neutral-900 mb-2">
              {searchTerm || filterBy !== 'all' ? 'Tidak ada hasil' : 'Belum ada analisis'}
            </h3>
            <p className="text-neutral-600 mb-6">
              {searchTerm || filterBy !== 'all' 
                ? 'Coba ubah filter atau kata kunci pencarian'
                : 'Mulai analisis kulit pertama Anda untuk melihat riwayat di sini'
              }
            </p>
            {!searchTerm && filterBy === 'all' && (
              <button className="btn-primary">
                Mulai Analisis
              </button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {sortedAnalyses.map((analysis) => (
              <div key={analysis.id} className="card hover:shadow-lg transition-shadow duration-200">
                <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-6">
                  {/* Image */}
                  <div className="flex-shrink-0">
                    <img
                      src={analysis.image}
                      alt="Analysis"
                      className="w-20 h-20 rounded-lg object-cover bg-neutral-100"
                    />
                  </div>

                  {/* Info */}
                  <div className="flex-1 space-y-2">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-neutral-900">
                          Analisis {new Date(analysis.date).toLocaleDateString('id-ID')}
                        </h3>
                        <p className="text-neutral-600">
                          Tipe Kulit: {analysis.skinType}
                        </p>
                      </div>
                      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getScoreColor(analysis.score)}`}>
                        {analysis.score}/10 - {getScoreLabel(analysis.score)}
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2">
                      {analysis.concerns.map((concern, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full"
                        >
                          {concern}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <button className="p-2 hover:bg-neutral-100 rounded-lg transition-colors duration-200">
                      <FiEye className="w-5 h-5 text-neutral-600" />
                    </button>
                    <button className="p-2 hover:bg-neutral-100 rounded-lg transition-colors duration-200">
                      <FiDownload className="w-5 h-5 text-neutral-600" />
                    </button>
                    <button 
                      onClick={() => handleDelete(analysis.id)}
                      className="p-2 hover:bg-red-100 rounded-lg transition-colors duration-200"
                    >
                      <FiTrash2 className="w-5 h-5 text-red-600" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 border border-neutral-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-neutral-50"
              >
                Sebelumnya
              </button>
              
              {[...Array(totalPages)].map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentPage(index + 1)}
                  className={`px-3 py-2 border rounded-lg ${
                    currentPage === index + 1
                      ? 'bg-primary-600 text-white border-primary-600'
                      : 'border-neutral-300 hover:bg-neutral-50'
                  }`}
                >
                  {index + 1}
                </button>
              ))}
              
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 border border-neutral-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-neutral-50"
              >
                Selanjutnya
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default History;
