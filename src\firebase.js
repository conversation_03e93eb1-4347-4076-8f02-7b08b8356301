// Import the functions you need from the SDKs you need
import { getAnalytics } from "firebase/analytics";
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey:
    process.env.REACT_APP_FIREBASE_API_KEY ||
    "AIzaSyD31R5coF9HreCyOzjjZncTMzBpVO-o8rw",
  authDomain:
    process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || "coba-aaf17.firebaseapp.com",
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || "coba-aaf17",
  storageBucket:
    process.env.REACT_APP_FIREBASE_STORAGE_BUCKET ||
    "coba-aaf17.firebasestorage.app",
  messagingSenderId:
    process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || "522899339211",
  appId:
    process.env.REACT_APP_FIREBASE_APP_ID ||
    "1:522899339211:web:b83d99179d30f23dae284e",
  measurementId:
    process.env.REACT_APP_FIREBASE_MEASUREMENT_ID || "G-3EKJ4X34SH",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
const db = getFirestore(app);
const auth = getAuth(app);
const storage = getStorage(app);
const analytics = getAnalytics(app);

export { analytics, auth, db, storage };
export default app;
