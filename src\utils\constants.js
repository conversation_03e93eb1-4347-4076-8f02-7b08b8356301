// Application constants

export const SKIN_TYPES = {
  NORMAL: 'normal',
  DRY: 'dry',
  OILY: 'oily',
  COMBINATION: 'combination',
  SENSITIVE: 'sensitive'
};

export const SKIN_TYPE_LABELS = {
  [SKIN_TYPES.NORMAL]: 'Normal',
  [SKIN_TYPES.DRY]: 'Kering',
  [SKIN_TYPES.OILY]: 'Berminyak',
  [SKIN_TYPES.COMBINATION]: 'Kombinasi',
  [SKIN_TYPES.SENSITIVE]: 'Sensitif'
};

export const SKIN_CONCERNS = {
  ACNE: 'acne',
  WRINKLES: 'wrinkles',
  DARK_SPOTS: 'dark_spots',
  LARGE_PORES: 'large_pores',
  DULLNESS: 'dullness',
  DRYNESS: 'dryness',
  OILINESS: 'oiliness',
  SENSITIVITY: 'sensitivity'
};

export const SKIN_CONCERN_LABELS = {
  [SKIN_CONCERNS.ACNE]: 'Jerawat',
  [SKIN_CONCERNS.WRINKLES]: '<PERSON><PERSON><PERSON>',
  [SKIN_CONCERNS.DARK_SPOTS]: 'Noda Hitam',
  [SKIN_CONCERNS.LARGE_PORES]: 'Pori-pori Besar',
  [SKIN_CONCERNS.DULLNESS]: 'Kulit Kusam',
  [SKIN_CONCERNS.DRYNESS]: 'Kulit Kering',
  [SKIN_CONCERNS.OILINESS]: 'Kulit Berminyak',
  [SKIN_CONCERNS.SENSITIVITY]: 'Kulit Sensitif'
};

export const PRODUCT_CATEGORIES = {
  CLEANSER: 'cleanser',
  TONER: 'toner',
  SERUM: 'serum',
  MOISTURIZER: 'moisturizer',
  SUNSCREEN: 'sunscreen',
  MASK: 'mask',
  EXFOLIANT: 'exfoliant'
};

export const PRODUCT_CATEGORY_LABELS = {
  [PRODUCT_CATEGORIES.CLEANSER]: 'Pembersih',
  [PRODUCT_CATEGORIES.TONER]: 'Toner',
  [PRODUCT_CATEGORIES.SERUM]: 'Serum',
  [PRODUCT_CATEGORIES.MOISTURIZER]: 'Pelembab',
  [PRODUCT_CATEGORIES.SUNSCREEN]: 'Sunscreen',
  [PRODUCT_CATEGORIES.MASK]: 'Masker',
  [PRODUCT_CATEGORIES.EXFOLIANT]: 'Eksfoliasi'
};

export const ANALYSIS_SCORE_RANGES = {
  EXCELLENT: { min: 8, max: 10, label: 'Sangat Baik', color: 'green' },
  GOOD: { min: 6, max: 7.9, label: 'Baik', color: 'yellow' },
  FAIR: { min: 4, max: 5.9, label: 'Cukup', color: 'orange' },
  POOR: { min: 0, max: 3.9, label: 'Perlu Perhatian', color: 'red' }
};

export const API_STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error'
};

export const LOCAL_STORAGE_KEYS = {
  AUTH_TOKEN: 'authToken',
  USER_DATA: 'user',
  ANALYSIS_CACHE: 'analysisCache',
  PREFERENCES: 'userPreferences'
};

export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  HISTORY: '/history',
  ARTICLES: '/articles',
  ABOUT: '/about',
  LOGIN: '/login',
  REGISTER: '/register',
  PROFILE: '/profile'
};

export const IMAGE_CONSTRAINTS = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ACCEPTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png'],
  MIN_RESOLUTION: { width: 640, height: 480 },
  MAX_RESOLUTION: { width: 4096, height: 4096 }
};

export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 50
};
