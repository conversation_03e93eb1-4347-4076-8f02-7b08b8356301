import React, { useState, useEffect } from 'react';
import { 
  FiCamera, 
  FiUpload, 
  FiUser, 
  <PERSON>Clock,
  FiTrendingUp,
  FiStar,
  FiEye
} from 'react-icons/fi';
import { authAPI } from '../services/authAPI';
import { skinAnalysisAPI } from '../services/skinAnalysisAPI';
import CameraCapture from '../components/SkinAnalysis/CameraCapture';
import AnalysisResult from '../components/SkinAnalysis/AnalysisResult';

const Dashboard = () => {
  const [user, setUser] = useState(null);
  const [analysisHistory, setAnalysisHistory] = useState([]);
  const [stats, setStats] = useState(null);
  const [showCamera, setShowCamera] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Get user data
      const currentUser = authAPI.getCurrentUser();
      setUser(currentUser);

      // Get recent analysis history
      const historyResponse = await skinAnalysisAPI.getAnalysisHistory(1, 5);
      setAnalysisHistory(historyResponse.data || []);

      // Get analysis statistics
      const statsResponse = await skinAnalysisAPI.getAnalysisStats();
      setStats(statsResponse.data || {});

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAnalysisComplete = (result) => {
    setCurrentAnalysis(result);
    setShowCamera(false);
    loadDashboardData(); // Refresh data
  };

  const quickStats = [
    {
      icon: FiClock,
      label: 'Total Analisis',
      value: stats?.totalAnalyses || 0,
      color: 'text-blue-600'
    },
    {
      icon: FiTrendingUp,
      label: 'Bulan Ini',
      value: stats?.thisMonth || 0,
      color: 'text-green-600'
    },
    {
      icon: FiStar,
      label: 'Rata-rata Skor',
      value: stats?.averageScore ? `${stats.averageScore}/10` : 'N/A',
      color: 'text-yellow-600'
    },
    {
      icon: FiEye,
      label: 'Terakhir Dilihat',
      value: stats?.lastAnalysis ? new Date(stats.lastAnalysis).toLocaleDateString('id-ID') : 'Belum ada',
      color: 'text-purple-600'
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-neutral-600">Memuat dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-neutral-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">
            Selamat datang, {user?.name || 'User'}!
          </h1>
          <p className="text-neutral-600">
            Kelola analisis kulit dan lihat rekomendasi produk skincare Anda
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat, index) => (
            <div key={index} className="card">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg bg-neutral-100 ${stat.color}`}>
                  <stat.icon className="w-6 h-6" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-neutral-600">{stat.label}</p>
                  <p className="text-2xl font-bold text-neutral-900">{stat.value}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Action Area */}
          <div className="lg:col-span-2">
            {currentAnalysis ? (
              <AnalysisResult 
                analysis={currentAnalysis} 
                onNewAnalysis={() => {
                  setCurrentAnalysis(null);
                  setShowCamera(true);
                }}
              />
            ) : showCamera ? (
              <CameraCapture 
                onAnalysisComplete={handleAnalysisComplete}
                onCancel={() => setShowCamera(false)}
              />
            ) : (
              <div className="card text-center">
                <div className="py-12">
                  <div className="w-24 h-24 gradient-bg rounded-full flex items-center justify-center mx-auto mb-6">
                    <FiCamera className="w-12 h-12 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-neutral-900 mb-4">
                    Mulai Analisis Kulit
                  </h2>
                  <p className="text-neutral-600 mb-8 max-w-md mx-auto">
                    Ambil foto wajah Anda untuk mendapatkan analisis kondisi kulit dan 
                    rekomendasi produk skincare yang tepat
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <button
                      onClick={() => setShowCamera(true)}
                      className="btn-primary flex items-center justify-center space-x-2"
                    >
                      <FiCamera className="w-5 h-5" />
                      <span>Ambil Foto</span>
                    </button>
                    <button className="btn-outline flex items-center justify-center space-x-2">
                      <FiUpload className="w-5 h-5" />
                      <span>Upload Foto</span>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Profile Card */}
            <div className="card">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                  <FiUser className="w-6 h-6 text-primary-600" />
                </div>
                <div className="ml-4">
                  <h3 className="font-semibold text-neutral-900">{user?.name}</h3>
                  <p className="text-sm text-neutral-600">{user?.email}</p>
                </div>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-neutral-600">Tipe Kulit:</span>
                  <span className="font-medium">{user?.skinType || 'Belum diketahui'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-600">Bergabung:</span>
                  <span className="font-medium">
                    {user?.createdAt ? new Date(user.createdAt).toLocaleDateString('id-ID') : 'N/A'}
                  </span>
                </div>
              </div>
            </div>

            {/* Recent Analysis */}
            <div className="card">
              <h3 className="font-semibold text-neutral-900 mb-4">Analisis Terbaru</h3>
              {analysisHistory.length > 0 ? (
                <div className="space-y-3">
                  {analysisHistory.slice(0, 3).map((analysis) => (
                    <div key={analysis.id} className="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                      <div>
                        <p className="text-sm font-medium text-neutral-900">
                          {new Date(analysis.createdAt).toLocaleDateString('id-ID')}
                        </p>
                        <p className="text-xs text-neutral-600">
                          Skor: {analysis.score}/10
                        </p>
                      </div>
                      <button 
                        onClick={() => setCurrentAnalysis(analysis)}
                        className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                      >
                        Lihat
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-neutral-600 text-sm">Belum ada analisis</p>
              )}
            </div>

            {/* Tips */}
            <div className="card">
              <h3 className="font-semibold text-neutral-900 mb-4">Tips Hari Ini</h3>
              <div className="space-y-3">
                <div className="p-3 bg-primary-50 rounded-lg">
                  <p className="text-sm text-primary-800">
                    💡 Gunakan pencahayaan alami saat mengambil foto untuk hasil analisis yang lebih akurat
                  </p>
                </div>
                <div className="p-3 bg-secondary-50 rounded-lg">
                  <p className="text-sm text-secondary-800">
                    🌟 Lakukan analisis secara rutin untuk memantau perkembangan kondisi kulit Anda
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
