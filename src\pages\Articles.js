import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  FiSearch, 
  FiFilter, 
  <PERSON><PERSON><PERSON>,
  FiUser,
  FiBookOpen,
  FiTrendingUp
} from 'react-icons/fi';
import { articlesAPI } from '../services/articlesAPI';

const Articles = () => {
  const [articles, setArticles] = useState([]);
  const [featuredArticles, setFeaturedArticles] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);

  // Mock data - replace with actual API calls
  const mockCategories = [
    { id: 'all', name: '<PERSON><PERSON><PERSON>ike<PERSON>', count: 24 },
    { id: 'skincare-basics', name: 'Dasar Skincare', count: 8 },
    { id: 'acne-treatment', name: 'Perawatan Jerawat', count: 6 },
    { id: 'anti-aging', name: 'Anti-Aging', count: 5 },
    { id: 'ingredients', name: 'Bahan Aktif', count: 5 }
  ];

  const mockFeaturedArticles = [
    {
      id: 1,
      title: 'Panduan Lengkap Skincare untuk Pemula',
      excerpt: 'Pelajari dasar-dasar skincare yang tepat untuk memulai perjalanan perawatan kulit Anda',
      image: '/api/placeholder/400/250',
      category: 'Dasar Skincare',
      author: 'Dr. Sarah Johnson',
      publishedAt: '2024-01-15',
      readTime: 8,
      featured: true
    },
    {
      id: 2,
      title: 'Mengatasi Jerawat dengan Bahan Alami',
      excerpt: 'Temukan cara efektif mengatasi jerawat menggunakan bahan-bahan alami yang mudah ditemukan',
      image: '/api/placeholder/400/250',
      category: 'Perawatan Jerawat',
      author: 'Dr. Maya Putri',
      publishedAt: '2024-01-12',
      readTime: 6,
      featured: true
    }
  ];

  const mockArticles = [
    {
      id: 3,
      title: 'Vitamin C dalam Skincare: Manfaat dan Cara Penggunaan',
      excerpt: 'Pelajari manfaat vitamin C untuk kulit dan cara menggunakannya dengan benar dalam rutinitas skincare',
      image: '/api/placeholder/300/200',
      category: 'Bahan Aktif',
      author: 'Dr. Lisa Chen',
      publishedAt: '2024-01-10',
      readTime: 5
    },
    {
      id: 4,
      title: 'Rutinitas Skincare Anti-Aging untuk Usia 30+',
      excerpt: 'Panduan lengkap perawatan anti-aging yang efektif untuk menjaga kulit tetap muda dan sehat',
      image: '/api/placeholder/300/200',
      category: 'Anti-Aging',
      author: 'Dr. Amanda Wilson',
      publishedAt: '2024-01-08',
      readTime: 7
    },
    {
      id: 5,
      title: 'Memahami Tipe Kulit dan Cara Merawatnya',
      excerpt: 'Kenali tipe kulit Anda dan temukan produk serta rutinitas perawatan yang tepat',
      image: '/api/placeholder/300/200',
      category: 'Dasar Skincare',
      author: 'Dr. Michael Brown',
      publishedAt: '2024-01-05',
      readTime: 6
    },
    {
      id: 6,
      title: 'Retinol vs Retinoid: Mana yang Lebih Baik?',
      excerpt: 'Perbedaan antara retinol dan retinoid, serta cara memilih yang tepat untuk kebutuhan kulit Anda',
      image: '/api/placeholder/300/200',
      category: 'Bahan Aktif',
      author: 'Dr. Jennifer Lee',
      publishedAt: '2024-01-03',
      readTime: 8
    }
  ];

  useEffect(() => {
    loadArticlesData();
  }, [currentPage, selectedCategory]);

  const loadArticlesData = async () => {
    try {
      setLoading(true);
      setError('');
      
      // In real implementation:
      // const [articlesRes, featuredRes, categoriesRes] = await Promise.all([
      //   articlesAPI.getArticles({ page: currentPage, category: selectedCategory }),
      //   articlesAPI.getFeaturedArticles(),
      //   articlesAPI.getCategories()
      // ]);
      
      // For now, use mock data
      setTimeout(() => {
        setArticles(mockArticles);
        setFeaturedArticles(mockFeaturedArticles);
        setCategories(mockCategories);
        setLoading(false);
      }, 500);
      
    } catch (err) {
      setError(err.message || 'Gagal memuat artikel');
      setLoading(false);
    }
  };

  const filteredArticles = articles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || 
                           article.category.toLowerCase().replace(/\s+/g, '-') === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-neutral-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-neutral-900 mb-4">
            Artikel Skincare
          </h1>
          <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
            Pelajari tips, trik, dan panduan lengkap untuk perawatan kulit yang optimal
          </p>
        </div>

        {/* Featured Articles */}
        {featuredArticles.length > 0 && (
          <section className="mb-12">
            <div className="flex items-center space-x-2 mb-6">
              <FiTrendingUp className="w-6 h-6 text-primary-600" />
              <h2 className="text-2xl font-bold text-neutral-900">Artikel Unggulan</h2>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredArticles.map((article) => (
                <Link
                  key={article.id}
                  to={`/articles/${article.id}`}
                  className="group"
                >
                  <div className="card hover:shadow-xl transition-all duration-300 overflow-hidden p-0">
                    <div className="relative">
                      <img
                        src={article.image}
                        alt={article.title}
                        className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                          Unggulan
                        </span>
                      </div>
                    </div>
                    
                    <div className="p-6">
                      <div className="flex items-center space-x-4 text-sm text-neutral-600 mb-3">
                        <span className="bg-neutral-100 px-2 py-1 rounded-full">
                          {article.category}
                        </span>
                        <div className="flex items-center space-x-1">
                          <FiClock className="w-4 h-4" />
                          <span>{article.readTime} min baca</span>
                        </div>
                      </div>
                      
                      <h3 className="text-xl font-bold text-neutral-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                        {article.title}
                      </h3>
                      
                      <p className="text-neutral-600 mb-4 line-clamp-2">
                        {article.excerpt}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <FiUser className="w-4 h-4 text-neutral-400" />
                          <span className="text-sm text-neutral-600">{article.author}</span>
                        </div>
                        <span className="text-sm text-neutral-500">
                          {formatDate(article.publishedAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </section>
        )}

        {/* Search and Filter */}
        <div className="card mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Cari artikel..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>

            {/* Category Filter */}
            <div className="flex items-center space-x-2">
              <FiFilter className="w-4 h-4 text-neutral-600" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="input-field w-auto"
              >
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name} ({category.count})
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Articles Grid */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-neutral-600">Memuat artikel...</p>
          </div>
        ) : error ? (
          <div className="card text-center py-12">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={loadArticlesData}
              className="btn-primary"
            >
              Coba Lagi
            </button>
          </div>
        ) : filteredArticles.length === 0 ? (
          <div className="card text-center py-12">
            <FiBookOpen className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-neutral-900 mb-2">
              Tidak ada artikel ditemukan
            </h3>
            <p className="text-neutral-600">
              Coba ubah kata kunci pencarian atau kategori
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredArticles.map((article) => (
              <Link
                key={article.id}
                to={`/articles/${article.id}`}
                className="group"
              >
                <div className="card hover:shadow-lg transition-all duration-300 overflow-hidden p-0">
                  <div className="relative">
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  
                  <div className="p-6">
                    <div className="flex items-center space-x-4 text-sm text-neutral-600 mb-3">
                      <span className="bg-neutral-100 px-2 py-1 rounded-full">
                        {article.category}
                      </span>
                      <div className="flex items-center space-x-1">
                        <FiClock className="w-4 h-4" />
                        <span>{article.readTime} min</span>
                      </div>
                    </div>
                    
                    <h3 className="text-lg font-bold text-neutral-900 mb-3 group-hover:text-primary-600 transition-colors duration-200 line-clamp-2">
                      {article.title}
                    </h3>
                    
                    <p className="text-neutral-600 mb-4 line-clamp-3">
                      {article.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <FiUser className="w-4 h-4 text-neutral-400" />
                        <span className="text-sm text-neutral-600">{article.author}</span>
                      </div>
                      <span className="text-sm text-neutral-500">
                        {formatDate(article.publishedAt)}
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Articles;
