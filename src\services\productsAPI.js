import api, { API_ENDPOINTS } from './api';

// Products API functions
export const productsAPI = {
  // Get product recommendations based on skin analysis
  getRecommendations: async (analysisId, params = {}) => {
    try {
      const {
        limit = 10,
        priceRange = '',
        brand = '',
        category = ''
      } = params;

      const response = await api.get(API_ENDPOINTS.PRODUCTS.RECOMMENDATIONS, {
        params: {
          analysisId,
          limit,
          priceRange,
          brand,
          category
        }
      });

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch product recommendations');
    }
  },

  // Search products
  searchProducts: async (query, params = {}) => {
    try {
      const {
        page = 1,
        limit = 20,
        category = '',
        brand = '',
        priceRange = '',
        skinType = '',
        sortBy = 'relevance',
        sortOrder = 'desc'
      } = params;

      const response = await api.get(API_ENDPOINTS.PRODUCTS.SEARCH, {
        params: {
          q: query,
          page,
          limit,
          category,
          brand,
          priceRange,
          skinType,
          sortBy,
          sortOrder
        }
      });

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to search products');
    }
  },

  // Get product details
  getProductById: async (productId) => {
    try {
      const response = await api.get(`${API_ENDPOINTS.PRODUCTS.DETAILS}/${productId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch product details');
    }
  },

  // Get products by category
  getProductsByCategory: async (category, params = {}) => {
    try {
      const {
        page = 1,
        limit = 20,
        brand = '',
        priceRange = '',
        skinType = '',
        sortBy = 'popularity',
        sortOrder = 'desc'
      } = params;

      const response = await api.get(`${API_ENDPOINTS.PRODUCTS.DETAILS}/category/${category}`, {
        params: {
          page,
          limit,
          brand,
          priceRange,
          skinType,
          sortBy,
          sortOrder
        }
      });

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch products by category');
    }
  },

  // Get featured products
  getFeaturedProducts: async (limit = 8) => {
    try {
      const response = await api.get(`${API_ENDPOINTS.PRODUCTS.DETAILS}/featured`, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch featured products');
    }
  },

  // Get product categories
  getCategories: async () => {
    try {
      const response = await api.get(`${API_ENDPOINTS.PRODUCTS.DETAILS}/categories`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch product categories');
    }
  },

  // Get product brands
  getBrands: async () => {
    try {
      const response = await api.get(`${API_ENDPOINTS.PRODUCTS.DETAILS}/brands`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch product brands');
    }
  },

  // Get similar products
  getSimilarProducts: async (productId, limit = 6) => {
    try {
      const response = await api.get(`${API_ENDPOINTS.PRODUCTS.DETAILS}/${productId}/similar`, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch similar products');
    }
  }
};

export default productsAPI;
