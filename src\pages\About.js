import React from 'react';
import { 
  FiCamera, 
  FiStar, 
  FiShield, 
  FiZap,
  FiUsers,
  FiAward,
  FiHeart,
  FiTarget
} from 'react-icons/fi';

const About = () => {
  const features = [
    {
      icon: FiCamera,
      title: 'Teknologi AI Canggih',
      description: 'Menggunakan Convolutional Neural Network (CNN) untuk analisis kulit yang akurat dan mendalam'
    },
    {
      icon: FiStar,
      title: 'Rekomendasi Personal',
      description: 'Algoritma machine learning yang memberikan rekomendasi produk skincare yang disesuaikan dengan kebutuhan unik kulit Anda'
    },
    {
      icon: FiShield,
      title: 'Keamanan Data',
      description: 'Privasi dan keamanan data pengguna adalah prioritas utama dengan enkripsi end-to-end'
    },
    {
      icon: FiZap,
      title: 'Analisis Real-time',
      description: 'Hasil analisis yang cepat dan akurat dalam hitungan detik menggunakan infrastruktur cloud terdepan'
    }
  ];

  const team = [
    {
      name: 'Dr. <PERSON>',
      role: 'Chief Technology Officer',
      image: '/api/placeholder/150/150',
      description: 'Ahli dermatologi dengan 15+ tahun pengalaman dalam penelitian skincare dan AI'
    },
    {
      name: 'Michael <PERSON>',
      role: 'Lead AI Engineer',
      image: '/api/placeholder/150/150',
      description: 'Spesialis machine learning dengan fokus pada computer vision dan deep learning'
    },
    {
      name: 'Dr. Maya Putri',
      role: 'Head of Research',
      image: '/api/placeholder/150/150',
      description: 'Peneliti skincare dengan publikasi internasional dalam bidang dermatologi digital'
    },
    {
      name: 'Alex Rodriguez',
      role: 'Product Manager',
      image: '/api/placeholder/150/150',
      description: 'Berpengalaman dalam pengembangan produk teknologi kesehatan dan beauty tech'
    }
  ];

  const stats = [
    { icon: FiUsers, number: '50K+', label: 'Pengguna Aktif' },
    { icon: FiCamera, number: '100K+', label: 'Analisis Selesai' },
    { icon: FiAward, number: '95%', label: 'Tingkat Akurasi' },
    { icon: FiHeart, number: '4.8/5', label: 'Rating Pengguna' }
  ];

  const values = [
    {
      icon: FiTarget,
      title: 'Akurasi',
      description: 'Kami berkomitmen memberikan hasil analisis yang akurat dan dapat diandalkan'
    },
    {
      icon: FiHeart,
      title: 'Kepedulian',
      description: 'Setiap fitur dirancang dengan mempertimbangkan kebutuhan dan kenyamanan pengguna'
    },
    {
      icon: FiShield,
      title: 'Kepercayaan',
      description: 'Transparansi dan keamanan data adalah fondasi hubungan kami dengan pengguna'
    },
    {
      icon: FiZap,
      title: 'Inovasi',
      description: 'Terus mengembangkan teknologi terdepan untuk solusi skincare yang lebih baik'
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="gradient-bg text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Tentang SkinCare AI
          </h1>
          <p className="text-xl md:text-2xl text-primary-100 max-w-3xl mx-auto">
            Revolusi dalam perawatan kulit dengan teknologi AI yang membantu Anda 
            menemukan produk skincare yang tepat
          </p>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-6">
                Misi Kami
              </h2>
              <p className="text-lg text-neutral-600 mb-6">
                SkinCare AI hadir untuk mendemokratisasi akses terhadap konsultasi skincare 
                berkualitas tinggi. Kami percaya bahwa setiap orang berhak mendapatkan 
                perawatan kulit yang tepat, tanpa harus mengeluarkan biaya mahal untuk 
                konsultasi dermatologis.
              </p>
              <p className="text-lg text-neutral-600 mb-8">
                Dengan menggabungkan kecerdasan buatan dan keahlian dermatologi, kami 
                menciptakan platform yang dapat menganalisis kondisi kulit dan memberikan 
                rekomendasi produk yang personal dan akurat.
              </p>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-600 mb-2">2023</div>
                  <div className="text-neutral-600">Tahun Didirikan</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-600 mb-2">15+</div>
                  <div className="text-neutral-600">Ahli Dermatologi</div>
                </div>
              </div>
            </div>
            <div className="relative">
              <img
                src="/api/placeholder/600/400"
                alt="SkinCare AI Technology"
                className="rounded-xl shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-tr from-primary-600/20 to-transparent rounded-xl"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 gradient-bg rounded-full flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-neutral-900 mb-2">
                  {stat.number}
                </div>
                <div className="text-neutral-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Teknologi Terdepan
            </h2>
            <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
              Kombinasi AI, machine learning, dan keahlian dermatologi untuk hasil terbaik
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="card">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 gradient-bg rounded-lg flex items-center justify-center flex-shrink-0">
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-neutral-900 mb-3">
                      {feature.title}
                    </h3>
                    <p className="text-neutral-600">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Nilai-Nilai Kami
            </h2>
            <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
              Prinsip yang memandu setiap keputusan dan inovasi yang kami buat
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
                  <value.icon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-neutral-900 mb-3">
                  {value.title}
                </h3>
                <p className="text-neutral-600">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              Tim Ahli Kami
            </h2>
            <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
              Gabungan expertise dari dermatologi, AI, dan teknologi untuk memberikan solusi terbaik
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="card text-center">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
                />
                <h3 className="text-xl font-semibold text-neutral-900 mb-2">
                  {member.name}
                </h3>
                <p className="text-primary-600 font-medium mb-3">
                  {member.role}
                </p>
                <p className="text-neutral-600 text-sm">
                  {member.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 gradient-bg text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Siap Memulai Perjalanan Skincare Anda?
          </h2>
          <p className="text-xl mb-8 text-primary-100">
            Bergabunglah dengan ribuan pengguna yang telah merasakan manfaat teknologi AI 
            untuk perawatan kulit yang lebih baik
          </p>
          <button className="bg-white text-primary-600 hover:bg-primary-50 font-semibold py-3 px-8 rounded-lg transition-colors duration-200">
            Mulai Analisis Gratis
          </button>
        </div>
      </section>
    </div>
  );
};

export default About;
