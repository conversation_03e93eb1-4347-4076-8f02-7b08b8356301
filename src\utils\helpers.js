// Utility helper functions

/**
 * Format currency to Indonesian Rupiah
 * @param {number} amount 
 * @returns {string}
 */
export const formatCurrency = (amount) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(amount);
};

/**
 * Format date to Indonesian locale
 * @param {string|Date} date 
 * @param {object} options 
 * @returns {string}
 */
export const formatDate = (date, options = {}) => {
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  };
  
  return new Date(date).toLocaleDateString('id-ID', { ...defaultOptions, ...options });
};

/**
 * Format relative time (e.g., "2 hari yang lalu")
 * @param {string|Date} date 
 * @returns {string}
 */
export const formatRelativeTime = (date) => {
  const now = new Date();
  const targetDate = new Date(date);
  const diffInSeconds = Math.floor((now - targetDate) / 1000);
  
  if (diffInSeconds < 60) return 'Baru saja';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit yang lalu`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam yang lalu`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari yang lalu`;
  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} bulan yang lalu`;
  
  return `${Math.floor(diffInSeconds / 31536000)} tahun yang lalu`;
};

/**
 * Validate email format
 * @param {string} email 
 * @returns {boolean}
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate password strength
 * @param {string} password 
 * @returns {object}
 */
export const validatePassword = (password) => {
  const minLength = 6;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  const score = [
    password.length >= minLength,
    hasUpperCase,
    hasLowerCase,
    hasNumbers,
    hasSpecialChar
  ].filter(Boolean).length;
  
  let strength = 'weak';
  if (score >= 4) strength = 'strong';
  else if (score >= 3) strength = 'medium';
  
  return {
    isValid: password.length >= minLength,
    strength,
    score,
    requirements: {
      minLength: password.length >= minLength,
      hasUpperCase,
      hasLowerCase,
      hasNumbers,
      hasSpecialChar
    }
  };
};

/**
 * Debounce function
 * @param {function} func 
 * @param {number} wait 
 * @returns {function}
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Throttle function
 * @param {function} func 
 * @param {number} limit 
 * @returns {function}
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Generate random ID
 * @param {number} length 
 * @returns {string}
 */
export const generateId = (length = 8) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * Truncate text with ellipsis
 * @param {string} text 
 * @param {number} maxLength 
 * @returns {string}
 */
export const truncateText = (text, maxLength) => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

/**
 * Convert file to base64
 * @param {File} file 
 * @returns {Promise<string>}
 */
export const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
};

/**
 * Validate image file
 * @param {File} file 
 * @param {object} constraints 
 * @returns {object}
 */
export const validateImageFile = (file, constraints = {}) => {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB
    acceptedFormats = ['image/jpeg', 'image/jpg', 'image/png'],
    minResolution = { width: 640, height: 480 },
    maxResolution = { width: 4096, height: 4096 }
  } = constraints;
  
  const errors = [];
  
  // Check file size
  if (file.size > maxSize) {
    errors.push(`Ukuran file terlalu besar. Maksimal ${Math.round(maxSize / 1024 / 1024)}MB`);
  }
  
  // Check file format
  if (!acceptedFormats.includes(file.type)) {
    errors.push(`Format file tidak didukung. Gunakan: ${acceptedFormats.join(', ')}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Get analysis score color and label
 * @param {number} score 
 * @returns {object}
 */
export const getAnalysisScoreInfo = (score) => {
  if (score >= 8) {
    return { color: 'green', label: 'Sangat Baik', bgColor: 'bg-green-100', textColor: 'text-green-800' };
  } else if (score >= 6) {
    return { color: 'yellow', label: 'Baik', bgColor: 'bg-yellow-100', textColor: 'text-yellow-800' };
  } else if (score >= 4) {
    return { color: 'orange', label: 'Cukup', bgColor: 'bg-orange-100', textColor: 'text-orange-800' };
  } else {
    return { color: 'red', label: 'Perlu Perhatian', bgColor: 'bg-red-100', textColor: 'text-red-800' };
  }
};
