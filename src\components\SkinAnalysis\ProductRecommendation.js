import React, { useState, useEffect } from 'react';
import { 
  FiX, 
  FiStar, 
  FiShoppingCart,
  FiExternalLink,
  FiHeart,
  FiInfo,
  FiLoader
} from 'react-icons/fi';
import { productsAPI } from '../../services/productsAPI';

const ProductRecommendation = ({ analysisId, skinType, concerns, onClose }) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'Se<PERSON><PERSON> Produk' },
    { id: 'cleanser', name: 'P<PERSON>bersih' },
    { id: 'toner', name: 'Toner' },
    { id: 'serum', name: 'Serum' },
    { id: 'moisturizer', name: '<PERSON>elembab' },
    { id: 'sunscreen', name: 'Sunscreen' }
  ];

  // Mock products data - replace with actual API call
  const mockProducts = [
    {
      id: 1,
      name: 'Gentle Foaming Cleanser',
      brand: 'SkinCare Pro',
      category: 'cleanser',
      price: 150000,
      rating: 4.5,
      image: '/api/placeholder/300/300',
      description: 'Pembersih wajah lembut yang cocok untuk kulit sensitif',
      benefits: ['Membersihkan kotoran', 'Tidak mengiritasi', 'pH balanced'],
      matchScore: 95
    },
    {
      id: 2,
      name: 'Hydrating Toner',
      brand: 'Beauty Lab',
      category: 'toner',
      price: 200000,
      rating: 4.7,
      image: '/api/placeholder/300/300',
      description: 'Toner yang memberikan hidrasi ekstra untuk kulit kering',
      benefits: ['Melembabkan', 'Menyeimbangkan pH', 'Menenangkan kulit'],
      matchScore: 88
    },
    {
      id: 3,
      name: 'Vitamin C Serum',
      brand: 'Glow Essentials',
      category: 'serum',
      price: 350000,
      rating: 4.8,
      image: '/api/placeholder/300/300',
      description: 'Serum vitamin C untuk mencerahkan dan melindungi kulit',
      benefits: ['Mencerahkan kulit', 'Antioksidan', 'Anti-aging'],
      matchScore: 92
    },
    {
      id: 4,
      name: 'Daily Moisturizer SPF 30',
      brand: 'Sun Shield',
      category: 'moisturizer',
      price: 180000,
      rating: 4.6,
      image: '/api/placeholder/300/300',
      description: 'Pelembab harian dengan perlindungan UV',
      benefits: ['Melembabkan', 'Perlindungan UV', 'Non-comedogenic'],
      matchScore: 90
    }
  ];

  useEffect(() => {
    loadRecommendations();
  }, [analysisId]);

  const loadRecommendations = async () => {
    try {
      setLoading(true);
      setError('');
      
      // In real implementation, call the API
      // const response = await productsAPI.getRecommendations(analysisId);
      // setProducts(response.data);
      
      // For now, use mock data
      setTimeout(() => {
        setProducts(mockProducts);
        setLoading(false);
      }, 1000);
      
    } catch (err) {
      setError(err.message || 'Gagal memuat rekomendasi produk');
      setLoading(false);
    }
  };

  const filteredProducts = selectedCategory === 'all' 
    ? products 
    : products.filter(product => product.category === selectedCategory);

  const formatPrice = (price) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(price);
  };

  const getMatchScoreColor = (score) => {
    if (score >= 90) return 'text-green-600 bg-green-100';
    if (score >= 80) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-200">
          <div>
            <h2 className="text-2xl font-bold text-neutral-900">Rekomendasi Produk</h2>
            <p className="text-neutral-600 mt-1">
              Produk yang cocok untuk kulit {skinType.toLowerCase()} Anda
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-neutral-100 rounded-lg transition-colors duration-200"
          >
            <FiX className="w-6 h-6 text-neutral-600" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Skin Analysis Summary */}
          <div className="bg-primary-50 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2 mb-3">
              <FiInfo className="w-5 h-5 text-primary-600" />
              <h3 className="font-semibold text-primary-900">Ringkasan Analisis</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-primary-800">Tipe Kulit:</span>
                <span className="ml-2 text-primary-700">{skinType}</span>
              </div>
              <div>
                <span className="font-medium text-primary-800">Perhatian Utama:</span>
                <span className="ml-2 text-primary-700">{concerns.join(', ')}</span>
              </div>
            </div>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap gap-2 mb-6">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                  selectedCategory === category.id
                    ? 'bg-primary-600 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="text-center py-12">
              <FiLoader className="w-8 h-8 text-primary-600 animate-spin mx-auto mb-4" />
              <p className="text-neutral-600">Memuat rekomendasi produk...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={loadRecommendations}
                className="btn-primary"
              >
                Coba Lagi
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredProducts.map((product) => (
                <div key={product.id} className="card hover:shadow-lg transition-shadow duration-200">
                  {/* Product Image */}
                  <div className="relative mb-4">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-48 object-cover rounded-lg bg-neutral-100"
                    />
                    <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium ${getMatchScoreColor(product.matchScore)}`}>
                      {product.matchScore}% Match
                    </div>
                    <button className="absolute top-2 left-2 p-2 bg-white rounded-full shadow-sm hover:bg-neutral-50 transition-colors duration-200">
                      <FiHeart className="w-4 h-4 text-neutral-600" />
                    </button>
                  </div>

                  {/* Product Info */}
                  <div className="space-y-3">
                    <div>
                      <h3 className="font-semibold text-neutral-900 line-clamp-2">
                        {product.name}
                      </h3>
                      <p className="text-sm text-neutral-600">{product.brand}</p>
                    </div>

                    <p className="text-sm text-neutral-600 line-clamp-2">
                      {product.description}
                    </p>

                    {/* Benefits */}
                    <div className="flex flex-wrap gap-1">
                      {product.benefits.slice(0, 2).map((benefit, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-secondary-100 text-secondary-800 text-xs rounded-full"
                        >
                          {benefit}
                        </span>
                      ))}
                      {product.benefits.length > 2 && (
                        <span className="px-2 py-1 bg-neutral-100 text-neutral-600 text-xs rounded-full">
                          +{product.benefits.length - 2} lainnya
                        </span>
                      )}
                    </div>

                    {/* Rating and Price */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-1">
                        <FiStar className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm font-medium text-neutral-900">
                          {product.rating}
                        </span>
                      </div>
                      <div className="text-lg font-bold text-primary-600">
                        {formatPrice(product.price)}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2 pt-2">
                      <button className="flex-1 btn-primary text-sm flex items-center justify-center space-x-1">
                        <FiShoppingCart className="w-4 h-4" />
                        <span>Beli</span>
                      </button>
                      <button className="btn-outline text-sm flex items-center justify-center space-x-1">
                        <FiExternalLink className="w-4 h-4" />
                        <span>Detail</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {filteredProducts.length === 0 && !loading && !error && (
            <div className="text-center py-12">
              <p className="text-neutral-600">Tidak ada produk ditemukan untuk kategori ini.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductRecommendation;
