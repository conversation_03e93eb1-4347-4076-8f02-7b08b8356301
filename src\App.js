// src/App.js
import { addDoc, collection, getDocs } from "firebase/firestore";
import { useEffect, useState } from "react";
import { db } from "./firebase";

function App() {
  const [users, setUsers] = useState([]);
  const usersCollection = collection(db, "users");

  // :white_check_mark: Add user
  const handleAddUser = async () => {
    try {
      await addDoc(usersCollection, {
        name: "<PERSON>",
        email: "<EMAIL>",
      });
      alert("User added!");
      fetchUsers(); // Refresh list
    } catch (error) {
      console.error("Error adding document: ", error);
    }
  };

  // :white_check_mark: Get users
  const fetchUsers = async () => {
    try {
      const querySnapshot = await getDocs(usersCollection);
      const usersData = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
      setUsers(usersData);
    } catch (error) {
      console.error("Error getting documents: ", error);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  return (
    <div style={{ padding: 20 }}>
      <h1>:clipboard: User List</h1>
      <button onClick={handleAddUser}>:heavy_plus_sign: Add User</button>
      <ul>
        {users.map((user) => (
          <li key={user.id}>
            <b>{user.name}</b> - {user.email}
          </li>
        ))}
      </ul>
    </div>
  );
}

export default App;
