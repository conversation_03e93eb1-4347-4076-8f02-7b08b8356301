import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  updateProfile,
} from "firebase/auth";
import { auth } from "../firebase";

// Authentication API functions using Firebase Auth
export const authAPI = {
  // User login
  login: async (email, password) => {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;

      // Get token
      const token = await user.getIdToken();

      // Store token and user data
      localStorage.setItem("authToken", token);
      localStorage.setItem(
        "user",
        JSON.stringify({
          uid: user.uid,
          email: user.email,
          name: user.displayName || user.email.split("@")[0],
          emailVerified: user.emailVerified,
        })
      );

      return {
        token,
        user: {
          uid: user.uid,
          email: user.email,
          name: user.displayName || user.email.split("@")[0],
          emailVerified: user.emailVerified,
        },
      };
    } catch (error) {
      throw new Error(error.message || "Login failed");
    }
  },

  // User registration
  register: async (userData) => {
    try {
      const { email, password, name } = userData;
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;

      // Update display name if provided
      if (name) {
        await updateProfile(user, { displayName: name });
      }

      // Get token
      const token = await user.getIdToken();

      // Store token and user data
      localStorage.setItem("authToken", token);
      localStorage.setItem(
        "user",
        JSON.stringify({
          uid: user.uid,
          email: user.email,
          name: name || user.email.split("@")[0],
          emailVerified: user.emailVerified,
        })
      );

      return {
        token,
        user: {
          uid: user.uid,
          email: user.email,
          name: name || user.email.split("@")[0],
          emailVerified: user.emailVerified,
        },
      };
    } catch (error) {
      throw new Error(error.message || "Registration failed");
    }
  },

  // User logout
  logout: async () => {
    try {
      await signOut(auth);

      // Clear local storage
      localStorage.removeItem("authToken");
      localStorage.removeItem("user");

      return { success: true };
    } catch (error) {
      // Even if API call fails, clear local storage
      localStorage.removeItem("authToken");
      localStorage.removeItem("user");
      throw new Error(error.message || "Logout failed");
    }
  },

  // Get user profile (from Firebase Auth)
  getProfile: async () => {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error("No user logged in");
      }

      const userData = {
        uid: user.uid,
        email: user.email,
        name: user.displayName || user.email.split("@")[0],
        emailVerified: user.emailVerified,
      };

      // Update stored user data
      localStorage.setItem("user", JSON.stringify(userData));

      return { user: userData };
    } catch (error) {
      throw new Error(error.message || "Failed to fetch profile");
    }
  },

  // Update user profile (Firebase Auth)
  updateUserProfile: async (profileData) => {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error("No user logged in");
      }

      await updateProfile(user, profileData);

      const userData = {
        uid: user.uid,
        email: user.email,
        name: user.displayName || user.email.split("@")[0],
        emailVerified: user.emailVerified,
      };

      // Update stored user data
      localStorage.setItem("user", JSON.stringify(userData));

      return { user: userData };
    } catch (error) {
      throw new Error(error.message || "Failed to update profile");
    }
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    const token = localStorage.getItem("authToken");
    const user = localStorage.getItem("user");
    return !!(token && user);
  },

  // Get current user from localStorage
  getCurrentUser: () => {
    const user = localStorage.getItem("user");
    return user ? JSON.parse(user) : null;
  },

  // Get auth token
  getToken: () => {
    return localStorage.getItem("authToken");
  },
};

export default authAPI;
